import React from 'react'
import { clusterApiUrl } from '@solana/web3.js'
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base'
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui'
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react'
import '@solana/wallet-adapter-react-ui/styles.css'

const network = WalletAdapterNetwork.Mainnet
const endpoint = clusterApiUrl(network)
console.log(`Solana 网络配置, network: ${network}, endpoint: ${endpoint}`)

export const SolanaWalletProvider: React.FC<any> = ({ children }) => {
  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={[]} autoConnect>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  )
}