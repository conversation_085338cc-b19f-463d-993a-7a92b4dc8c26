import { useModel } from '@umijs/max'
import React, { useEffect, useState } from 'react'
import { Spin, Button, Space, message } from 'antd'
import { useWallet } from '@solana/wallet-adapter-react'
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui'

const GlobalHeaderInfo: React.FC = () => {
  const { connected, publicKey } = useWallet()
  const [refreshing, setRefreshing] = useState(false)
  const [memberCheckShown, setMemberCheckShown] = useState(false)
  const { userBalance, loading, currentMultisig, refreshMultisigs } = useModel('multisig')

  useEffect(() => {
    refreshMultisigs()
  }, [])

  // 重置成员检查状态
  useEffect(() => {
    setMemberCheckShown(false)
  }, [publicKey, currentMultisig])

  // 检查用户是否为多签成员
  useEffect(() => {
    if (!loading && connected && publicKey && currentMultisig && !memberCheckShown) {
      const userPublicKeyString = publicKey.toBase58()
      const isMember = currentMultisig.members.some((member: any) => member.address === userPublicKeyString)
      if (!isMember) {
        message.warning({ content: 'This wallet is not a member of this Squad.', duration: 8, key: 'member-check' })
        setMemberCheckShown(true)
      }
    }
  }, [loading, connected, publicKey, currentMultisig, memberCheckShown])

  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      await refreshMultisigs(true) // 强制刷新
    } finally {
      setRefreshing(false)
    }
  }

  return (
    <div style={{ marginBottom: 16, padding: '8px', background: '#fff', borderRadius: '6px', boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', fontWeight: 500, color: '#666' }}>
        <Space>
          <Button type="primary" size="small" loading={refreshing} onClick={handleRefresh} title="刷新数据">
            刷新
          </Button>
        </Space>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        {connected && publicKey && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', padding: '4px 8px', backgroundColor: '#f0f2f5', borderRadius: '6px' }}>
            <div style={{ fontSize: '16px', fontWeight: '600', lineHeight: 1.6, color: '#52c41a' }}>
              {loading ? (<Spin size="small" />) : (`${userBalance.toFixed(5)} SOL`)}
            </div>
          </div>
        )}
        <WalletMultiButton style={{ height: '36px', fontSize: '14px', borderRadius: '6px' }} />
      </div>
    </div>
  )
}

export default GlobalHeaderInfo
