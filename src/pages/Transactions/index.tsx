import { useModel } from '@umijs/max'
import * as multisig from '@sqds/multisig'
import { useWallet } from '@solana/wallet-adapter-react'
import React, { useState, useCallback, useEffect } from 'react'
import { Table, Button, Tag, message, Space, Card, Descriptions, Spin } from 'antd'
import { CheckCircleOutlined, CloseCircleOutlined, PlayCircleOutlined } from '@ant-design/icons'
import { PublicKey, TransactionMessage, VersionedTransaction, ComputeBudgetProgram } from '@solana/web3.js'
import Api from '@/services/api'
import { formatTime } from '@/utils/util'
import { SQUADS_PROGRAM_ID_V4_PUBKEY, COMPUTE_UNIT_LIMITS, PRIORITY_FEES } from '@/constants'

const TransactionsPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const { publicKey, signTransaction } = useWallet()
  const { loading: multisigLoading, currentMultisig, refreshMultisigs } = useModel('multisig')
  const [transactions, setTransactions] = useState<[]>([])
  const [loading, setLoading] = useState(false)
  const [actionLoading, setActionLoading] = useState<string>('')
  const [expandedRowKeys, setExpandedRowKeys] = useState<number[]>([])
  const [expandedRowData, setExpandedRowData] = useState<Record<number, any>>({})
  const [expandedRowLoading, setExpandedRowLoading] = useState<Record<number, boolean>>({})

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: false,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
  })

  const loadTransactions = useCallback(async (page?: number, pageSize?: number) => {
    if (!currentMultisig || !publicKey) return
    setLoading(true)
    try {
      const { transactions, pagination: { page: paginationPage, total: paginationTotal } } = await Api.getTransactions(page || pagination.current, pageSize || pagination.pageSize)
      setTransactions(transactions)
      setPagination(prev => ({
        ...prev,
        current: paginationPage,
        total: paginationTotal,
      }))
    } catch (error: any) {
      messageApi.error(`加载交易失败, error: ${error.message}`)
      setTransactions([])
      setPagination(prev => ({
        ...prev,
        current: 1,
        total: 0,
      }))
    } finally {
      setLoading(false)
    }
  }, [currentMultisig, publicKey])

  useEffect(() => {
    loadTransactions()
  }, [loadTransactions])

  useEffect(() => {
    refreshMultisigs()
  }, [])

  const handleTableChange = useCallback(({ current, pageSize }: any) => {
    loadTransactions(current, pageSize)
  }, [loadTransactions])

  // 获取单个交易详情
  const loadTransactionDetail = useCallback(async (transactionIndex: number) => {
    if (expandedRowData[transactionIndex]) return // 已经加载过了

    setExpandedRowLoading(prev => ({ ...prev, [transactionIndex]: true }))
    try {
      const detail = await Api.getTransaction(transactionIndex)
      setExpandedRowData(prev => ({ ...prev, [transactionIndex]: detail }))
    } catch (error: any) {
      messageApi.error(`加载交易详情失败: ${error.message}`)
    } finally {
      setExpandedRowLoading(prev => ({ ...prev, [transactionIndex]: false }))
    }
  }, [expandedRowData])

  const handleExpand = useCallback((expanded: boolean, record: any) => {
    // 非转账数据不允许展开
    if (!record.chain) return

    const transactionIndex = record.transactionIndex
    if (expanded) {
      setExpandedRowKeys(prev => [...prev, transactionIndex])
      loadTransactionDetail(transactionIndex)
    } else {
      setExpandedRowKeys(prev => prev.filter(key => key !== transactionIndex))
    }
  }, [loadTransactionDetail])

  const handleVote = useCallback(async (transactionIndex: number, vote: any) => {
    if (!signTransaction || !publicKey || !currentMultisig) return
    const currentUser = currentMultisig.members.find(member => member.address === publicKey.toBase58())
    if (!currentUser) return messageApi.error('当前钱包不是多签成员')
    if (!currentUser.permissions.includes('Voter')) return messageApi.error('您没有投票权限（需要 Voter 权限）')

    try {
      setActionLoading(`${vote}-${transactionIndex}`)
      const multisigPda = new PublicKey(currentMultisig.multisigAccount)
      const instruction = vote === 'approve'
        ? multisig.instructions.proposalApprove({ multisigPda, transactionIndex: BigInt(transactionIndex), member: publicKey, programId: SQUADS_PROGRAM_ID_V4_PUBKEY })
        : multisig.instructions.proposalReject({ multisigPda, transactionIndex: BigInt(transactionIndex), member: publicKey, programId: SQUADS_PROGRAM_ID_V4_PUBKEY })

      const { blockhash } = await Api.getBlockhash()

      // 添加计算单元限制和优先费用指令来优化手续费
      const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
        units: COMPUTE_UNIT_LIMITS.VOTE_TRANSACTION,
      })

      const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: PRIORITY_FEES.VOTE_TRANSACTION,
      })

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [computeUnitLimitIx, computeUnitPriceIx, instruction]
      })

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message())
      const signedTransaction = await signTransaction(transaction)

      // 提交投票
      const { result: { value: { err } } } = await Api.voteTransaction({ transactionIndex, userPublicKey: publicKey.toBase58(), signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64')})
      if (err) {
        messageApi.error(`failed, error: ${err}`)
      } else {
        messageApi.success('success')
        await loadTransactions()
      }
    } catch (error: any) {
      messageApi.warning(`投票失败, message: ${error.message}`)
    } finally {
      setActionLoading('')
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions])

  const handleExecute = useCallback(async (transactionIndex: number) => {
    if (!signTransaction || !publicKey || !currentMultisig) return

    // 检查用户 Executor 权限
    const currentUser = currentMultisig.members.find(member => member.address === publicKey.toBase58())
    if (!currentUser) return messageApi.error('当前钱包不是多签成员')
    if (!currentUser.permissions.includes('Executor')) return messageApi.error('您没有执行权限（需要 Executor 权限）')

    try {
      setActionLoading(`execute-${transactionIndex}`)
      const { keys, programId, data } = await Api.buildExecuteInstruction({ transactionIndex, executorPublicKey: publicKey.toBase58() })

      const instruction = {
        keys: keys.map(({ pubkey, isSigner, isWritable }: any) => ({ pubkey: new PublicKey(pubkey), isSigner: isSigner, isWritable: isWritable })),
        programId: new PublicKey(programId),
        data: Buffer.from(data),
      }

      // 创建交易消息
      const { blockhash } = await Api.getBlockhash()

      // 添加计算单元限制和优先费用指令来优化手续费
      const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
        units: COMPUTE_UNIT_LIMITS.EXECUTE_TRANSACTION,
      })

      const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: PRIORITY_FEES.EXECUTE_TRANSACTION,
      })

      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [computeUnitLimitIx, computeUnitPriceIx, instruction]
      })

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message())
      const signedTransaction = await signTransaction(transaction)

      // 执行交易
      const { result: { value: { err } } } = await Api.executeTransaction({
        multisigAddress: currentMultisig.multisigAccount,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
      })

      if (err) {
        messageApi.error(`failed, error: ${err}`)
      } else {
        messageApi.success('success')
        await loadTransactions()
      }
    } catch (error: any) {
      console.error(`执行交易失败, error: ${error.message}`)
      messageApi.error(`执行交易失败, error: ${error.message}`)
    } finally {
      setActionLoading('')
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions])

  const handleCancel = useCallback(async (transactionIndex: number) => {
    if (!signTransaction || !publicKey || !currentMultisig) return

    const currentUser = currentMultisig.members.find((member: any) => member.address === publicKey.toBase58())
    if (!currentUser) return messageApi.error('当前钱包不是多签成员')

    try {
      setActionLoading(`cancel-${transactionIndex}`)

      const multisigPda = new PublicKey(currentMultisig.multisigAccount)
      const instruction = multisig.instructions.proposalCancel({ multisigPda, transactionIndex: BigInt(transactionIndex), member: publicKey, programId: SQUADS_PROGRAM_ID_V4_PUBKEY })

      const { blockhash } = await Api.getBlockhash()

      // 添加计算单元限制和优先费用指令来优化手续费
      const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
        units: COMPUTE_UNIT_LIMITS.CANCEL_TRANSACTION,
      })

      const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: PRIORITY_FEES.CANCEL_TRANSACTION,
      })

      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [computeUnitLimitIx, computeUnitPriceIx, instruction]
      })

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message())
      const signedTransaction = await signTransaction(transaction)

      // 提交取消
      const { result: { value: { err } } } = await Api.cancelTransaction({ signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64') })
      if (err) {
        messageApi.error(`failed, error: ${err}`)
      } else {
        messageApi.success('success')
        await loadTransactions()
      }
    } catch (error: any) {
      console.error(`取消交易失败, error: ${error.message}`)
      messageApi.error(`取消交易失败, error: ${error.message}`)
    } finally {
      setActionLoading('')
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions])

  const expandedRowRender = useCallback((record: any) => {
    const { transactionIndex, from, to, creator, votes, chain } = record
    const isLoading = expandedRowLoading[transactionIndex]
    const { createdAt, memo } = expandedRowData[transactionIndex] || {}

    // 非转账数据，显示提示信息
    if (!chain) {
      return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <Tag color="orange" style={{ fontSize: '14px', padding: '8px 16px' }}>
            非转账类型数据，暂无法解析详细信息
          </Tag>
        </div>
      )
    }

    if (isLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="small" />
          <span style={{ marginLeft: 8 }}>加载详情中...</span>
        </div>
      )
    }

    const renderConfirmed = (votes: any[]) => {
      if (!votes || !Array.isArray(votes) || votes.length === 0) return '-'
      return votes.filter(data => data.vote === 'Approve').map((vote, index) => (
        <Tag key={index} color='green' style={{ marginBottom: 4 }}>
          {vote.member}
        </Tag>
      ))
    }

    const renderRejected = (votes: any[]) => {
      if (!votes || !Array.isArray(votes) || votes.length === 0) return '-'
      return votes.filter(data => data.vote === 'Reject').map((vote, index) => (
        <Tag key={index} color='red' style={{ marginBottom: 4 }}>
          {vote.member}
        </Tag>
      ))
    }

    return (
      <Descriptions bordered size="small" column={2}>
        <Descriptions.Item label="Author">{creator || '-'}</Descriptions.Item>
        <Descriptions.Item label="Created on">{formatTime(createdAt)}</Descriptions.Item>
        <Descriptions.Item label="From">{from || '-'}</Descriptions.Item>
        <Descriptions.Item label="To">{to || '-'}</Descriptions.Item>
        <Descriptions.Item label="Confirmed" span={3}><div>{renderConfirmed(votes)}</div></Descriptions.Item>
        <Descriptions.Item label="Rejected" span={3}><div>{renderRejected(votes)}</div></Descriptions.Item>
        <Descriptions.Item label="Memo" span={3}>{memo || '-'}</Descriptions.Item>
      </Descriptions>
    )
  }, [expandedRowLoading, expandedRowData])

  const columns = [
    {
      title: 'Index',
      dataIndex: 'transactionIndex',
      key: 'transactionIndex',
      width: 60,
      render: (index: number) => `#${index}`,
    },
    {
      title: 'Amount',
      key: 'amount',
      width: 120,
      render: (_: any, record: any) => {
        if (!record.chain) {
          return {
            children: (
              <div style={{ textAlign: 'left', padding: '8px' }}>
                <Tag color="orange" style={{ fontSize: '13px', padding: '4px 12px' }}>非转账类型数据，暂无法解析</Tag>
              </div>
            ),
            props: { colSpan: 6 },
          }
        }
        return `${record.amount} ${record.chain}`
      },
    },
    {
      title: 'To',
      dataIndex: 'to',
      key: 'to',
      width: 120,
      render: (_: any, record: any) => {
        if (!record.chain) return { props: { colSpan: 0} }
        return `${record.to || '-'}`
      },
    },
    {
      title: 'UpdatedAt',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 140,
      render: (time: string | null, record: any) => {
        if (!record.chain) return { props: { colSpan: 0 } }
        return formatTime(time)
      },
    },
    // {
    //   title: 'CreatedAt',
    //   dataIndex: 'createdAt',
    //   key: 'createdAt',
    //   width: 140,
    //   render: (time: string | null) => formatTime(time),
    // },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 60,
      render: (status: string, record: any) => {
        if (!record.chain) return { props: { colSpan: 0 } }

        let color = 'default'
        let text = status
        switch (status) {
          case 'Active':
            color = 'processing'
            text = 'Active'
            break
          case 'Approved':
            color = 'success'
            text = 'Ready'
            break
          case 'Executed':
            color = 'success'
            text = 'Executed'
            break
          case 'Cancelled':
            color = 'default'
            text = 'Cancelled'
            break
          case 'Rejected':
            color = 'error'
            text = 'Cancelled'
            break
        }
        return <Tag color={color}>{text}</Tag>
      },
    },
    {
      title: 'Vote',
      key: 'progress',
      width: 60,
      render: (_: any, record: any) => {
        if (!record.chain) return { props: { colSpan: 0 } }
        return `${record.approvals || 0}/${record.threshold || 0}`
      },
    },
    {
      title: 'Action',
      key: 'action',
      width: 120,
      render: (_: any, { transactionIndex, status, votes }: any) => {
        // 非转账数据或无效数据
        if (!votes || !Array.isArray(votes)) return { props: { colSpan: 0 } }

        // 非成员
        if (!publicKey || !currentMultisig) return null
        const currentUser = currentMultisig.members.find((member: any) => member.address === publicKey.toBase58())
        if (!currentUser) return null

        // 成员
        const isApprove = votes.find((data: any) => data.member === publicKey.toBase58() && data.vote === 'Approve')
        const isReject = votes.find((data: any) => data.member === publicKey.toBase58() && data.vote === 'Reject')
        const isCancel = votes.find((data: any) => data.member === publicKey.toBase58() && data.vote === 'Cancel')

        if (status === 'Active') {
          if (currentUser.permissions.includes('Voter')) {
            if (isApprove || isReject) {
              return null
            } else {
              return (
                <Space>
                  <Button type="primary" size="small" loading={actionLoading === `approve-${transactionIndex}`} onClick={() => handleVote(transactionIndex, 'approve')} icon={<CheckCircleOutlined />}>
                    Approve
                  </Button>
                  <Button danger size="small" loading={actionLoading === `reject-${transactionIndex}`} onClick={() => handleVote(transactionIndex, 'reject')} icon={<CloseCircleOutlined />}>
                    Reject
                  </Button>
                </Space>
              )
            }
          } else {
            return null
          }
        }

        if (status === 'Approved') {
          if (currentUser.permissions.includes('Executor')) {
            if (isCancel) {
              return (
                <Space>
                  <Button type="primary" size="small" loading={actionLoading === `execute-${transactionIndex}`} onClick={() => handleExecute(transactionIndex)} icon={<PlayCircleOutlined />}>
                    Execute
                  </Button>
                </Space>
              )
            } else {
              return (
                <Space>
                  <>
                    <Button type="primary" size="small" loading={actionLoading === `execute-${transactionIndex}`} onClick={() => handleExecute(transactionIndex)} icon={<PlayCircleOutlined />}>
                      Execute
                    </Button>
                    <Button size="small" loading={actionLoading === `cancel-${transactionIndex}`} onClick={() => handleCancel(transactionIndex)}>
                      Cancel
                    </Button>
                  </>
                </Space>
              )
            }
          }
        }
        return null
      },
    },
  ]

  return (
    <>
      {contextHolder}
      <Card>
        <Table bordered columns={columns} dataSource={transactions} loading={loading || multisigLoading} rowKey="transactionIndex" pagination={pagination} onChange={handleTableChange} expandable={{
          expandedRowRender,
          expandedRowKeys,
          onExpand: handleExpand,
          expandRowByClick: false,
          rowExpandable: (record) => !!record.chain, // 只有转账数据才能展开
        }}/>
      </Card>
    </>
  )
}

export default TransactionsPage