import { useModel } from '@umijs/max'
import * as multisig from '@sqds/multisig'
import { SendOutlined } from '@ant-design/icons'
import React, { useState, useCallback, useEffect } from 'react'
import { useWallet, useConnection } from '@solana/wallet-adapter-react'
import { Form, Input, Button, Card, Select, message, InputNumber, Space, Descriptions, Table } from 'antd'
import { TOKEN_PROGRAM_ID, getAssociatedTokenAddress, createTransferInstruction } from '@solana/spl-token'
import { PublicKey, SystemProgram, TransactionMessage, VersionedTransaction, LAMPORTS_PER_SOL, ComputeBudgetProgram } from '@solana/web3.js'
import Api from '@/services/api'
import AddressCopy from '@/components/AddressCopy'
import { SQUADS_PROGRAM_ID_V4_PUBKEY, COMPUTE_UNIT_LIMITS, PRIORITY_FEES } from '@/constants'

const { Option } = Select

const SendPage: React.FC = () => {
  const [form] = Form.useForm()
  const { connected } = useWallet()
  const { connection } = useConnection()
  const [assets, setAssets] = useState<[]>([])
  const [loading, setLoading] = useState(false)
  const { publicKey, signTransaction } = useWallet()
  const [recipients, setRecipients] = useState<[]>([])
  const [messageApi, contextHolder] = message.useMessage()
  const [recipientsLoading, setRecipientsLoading] = useState(false)
  const { multisigs, currentMultisig, loading: multisigLoading, refreshMultisigs } = useModel('multisig')

  useEffect(() => {
    refreshMultisigs()
  }, [])

  const loadRecipients = useCallback(async () => {
    setRecipientsLoading(true)
    const tos = await Api.getRecipients()
    setRecipients(tos)
    setRecipientsLoading(false)
  }, [])

  useEffect(() => {
    loadRecipients()
  }, [loadRecipients])

  useEffect(() => {
    const buildAssets = () => {
      const assetsList: any = []
      if (!connected || multisigs.length === 0) return setAssets([])
        multisigs.forEach(({ vault: { assets } }: any) => {
        assets.forEach(({ symbol, address, balance, value  }: any) => assetsList.push({ symbol, address, balance, value }))
      })
      setAssets(assetsList)
    }
    buildAssets()
  }, [connected, multisigs])

  const handleTransfer = useCallback(async (values: any) => {
    try {
      setLoading(true)
      const { recipient, amount, selectedToken } = values

      if (!publicKey) throw new Error('请先连接钱包')
      if (!currentMultisig) throw new Error('多签账户未加载')
      const currentUser = currentMultisig.members.find(member => member.address === publicKey.toBase58())
      if (!currentUser) throw new Error('当前钱包不是多签成员')
      if (!currentUser.permissions.includes('Proposer')) throw new Error('您没有创建转账交易的权限（需要 Proposer 权限）')
      if (!recipient) throw new Error('请选择收款地址')
      if (!amount || parseFloat(amount) <= 0) throw new Error('请输入有效的转账金额')
      if (!selectedToken) throw new Error('请选择币种')

      const recipientPubkey = new PublicKey(recipient)
      const vaultPda = new PublicKey(currentMultisig.vault.address)
      const multisigPda = new PublicKey(currentMultisig.multisigAccount)

      // 构建交易数据
      let transferInstruction
      if (selectedToken === 'sol') {
        const transferAmount = Math.floor(parseFloat(amount) * LAMPORTS_PER_SOL)
        if (currentMultisig.vault.balance < transferAmount) throw new Error(`sol余额不足。当前余额: ${currentMultisig.vault.balanceSOL} sol`)
        transferInstruction = SystemProgram.transfer({
          fromPubkey: vaultPda,
          toPubkey: recipientPubkey,
          lamports: transferAmount, // 包含精度
        })
      } else {
        const selectedAsset = currentMultisig.vault.assets.find(asset => asset.symbol === selectedToken)
        if (!selectedAsset || !selectedAsset.mint || selectedAsset.decimals === undefined) throw new Error('未找到对应的Token信息')

        const transferAmount = Math.floor(parseFloat(amount) * Math.pow(10, selectedAsset.decimals))

        if (selectedAsset.balance < parseFloat(amount)) throw new Error(`${selectedAsset.symbol}余额不足。当前余额: ${selectedAsset.balance.toFixed(6)} ${selectedAsset.symbol}`)

        const tokenMintPubkey = new PublicKey(selectedAsset.mint)
        const vaultTokenAccount = await getAssociatedTokenAddress(tokenMintPubkey, vaultPda, true)
        const recipientTokenAccount = await getAssociatedTokenAddress(tokenMintPubkey, recipientPubkey)

        transferInstruction = createTransferInstruction(
          vaultTokenAccount,
          recipientTokenAccount,
          vaultPda,
          transferAmount, // 不包含精度
          [],
          TOKEN_PROGRAM_ID
        )
      }
      const { blockhash } = await Api.getBlockhash()
      const transferMessage = new TransactionMessage({ payerKey: vaultPda, recentBlockhash: blockhash, instructions: [transferInstruction] })
      const nextTransactionIndex = BigInt(currentMultisig.transactionIndex) + 1n

      // 创建交易并投票
      const createIx = multisig.instructions.vaultTransactionCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey,
        vaultIndex: 0,
        ephemeralSigners: 0,
        transactionMessage: transferMessage,
        memo: `Transfer ${values.amount} to ${values.recipient}`,
        programId: SQUADS_PROGRAM_ID_V4_PUBKEY,
      })

      const proposalIx = multisig.instructions.proposalCreate({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        creator: publicKey,
        isDraft: false,
        programId: SQUADS_PROGRAM_ID_V4_PUBKEY,
      })

      const approveIx = multisig.instructions.proposalApprove({
        multisigPda,
        transactionIndex: nextTransactionIndex,
        member: publicKey,
        programId: SQUADS_PROGRAM_ID_V4_PUBKEY,
      })

      // 添加计算单元限制和优先费用指令来优化手续费
      const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
        units: COMPUTE_UNIT_LIMITS.CREATE_TRANSACTION,
      })

      const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: PRIORITY_FEES.CREATE_TRANSACTION,
      })

      const combinedMessage = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [computeUnitLimitIx, computeUnitPriceIx, createIx, proposalIx, approveIx],
      })
      const combinedTx = new VersionedTransaction(combinedMessage.compileToV0Message())

      // phantom 签名
      const signedTx = await signTransaction(combinedTx)

      // 广播上链
      const { result: { value: { err } } } = await Api.createTransfer({ signedTransaction: Buffer.from(signedTx.serialize()).toString('base64') })
      if (err) {
        messageApi.error(`failed, error: ${err}`)
      } else {
        messageApi.success('success')
      }
    } catch (error: any) {
      console.error(`failed: ${error.message}`)
      messageApi.error(`failed: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }, [publicKey, signTransaction, currentMultisig, connection])

  const columns = [
    {
      title: 'Coin',
      dataIndex: 'symbol',
      key: 'symbol',
      render: (_: any, record: any) => record.symbol
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
      render: (_: any, record: any) => (
        <div>
          <AddressCopy address={record.address || ''} showFullAddress={true} />
        </div>
      )
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      render: (v: number) => v.toFixed(5)
    }
  ]

  return (
    <>
      {contextHolder}
      <div>
        <Card loading={multisigLoading} style={{ margin: '0px 0px 15px 0px' }}>
          <Descriptions bordered>
            <Descriptions.Item label="Squad Vault" span={2}>
              <AddressCopy address={currentMultisig?.vault.address || ''} showFullAddress={true} />
            </Descriptions.Item>
            <Descriptions.Item label="Multisig Account" span={1}>
              <AddressCopy address={currentMultisig?.multisigAccount || ''} showFullAddress={true} />
            </Descriptions.Item>
          </Descriptions>
        </Card>
        <Card loading={multisigLoading} style={{ margin: '0px 0px 15px 0px' }}>
          <Form form={form} layout="vertical" onFinish={handleTransfer}>
            <Form.Item label="From">
              <Input value={currentMultisig?.vault.address || ''} disabled style={{ backgroundColor: '#f5f5f5' }} />
            </Form.Item>
            <Form.Item name="recipient" label="To" rules={[{ required: true, message: '请选择To地址' }]}>
              <Select placeholder="选择To地址" loading={recipientsLoading} disabled={loading} optionFilterProp="children">
                {recipients.map(address => (
                  <Option key={address} value={address}>
                    <div style={{ fontSize: '12px', color: '#666' }}>{address}</div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="Amount">
              <Space.Compact style={{ width: '100%' }}>
                <Form.Item name="amount" noStyle rules={[{ required: true, message: '请输入转账金额' }]}>
                  <InputNumber placeholder="0" disabled={loading} style={{ width: '70%' }} />
                </Form.Item>
                <Form.Item name="selectedToken" noStyle rules={[{ required: true, message: '请选择币种' }]}>
                  <Select placeholder="请选择币种" disabled={loading} style={{ width: '30%' }}>
                    {currentMultisig?.vault.assets.map((asset) => (
                      <Option key={asset.symbol} value={asset.symbol}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span>{asset.symbol}</span>
                          <span style={{ fontSize: '12px', color: '#666' }}>余额: {asset.balance.toFixed(6)} {asset.symbol}</span>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Space.Compact>
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" loading={loading} disabled={!publicKey || !currentMultisig || loading} icon={<SendOutlined />} block>
                创建转账交易
              </Button>
            </Form.Item>
          </Form>
        </Card>
        <Table columns={columns} dataSource={assets} rowKey={r => r.symbol + r.address} loading={multisigLoading} pagination={false} bordered />
      </div>
    </>
  )
}

export default SendPage
