import { PublicKey } from '@solana/web3.js'

export const API_BASE_URL = 'http://localhost:3001';
export const SQUADS_PROGRAM_ID_V4_PUBKEY = new PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')

// 不同交易类型的推荐计算单元设置
export const COMPUTE_UNIT_LIMITS = {
  // 创建交易：包含 vaultTransactionCreate + proposalCreate + proposalApprove
  CREATE_TRANSACTION: 300_000,
  // 投票交易：proposalApprove 或 proposalReject
  VOTE_TRANSACTION: 100_000,
  // 执行交易：vaultTransactionExecute
  EXECUTE_TRANSACTION: 200_000,
  // 取消交易：proposalCancel
  CANCEL_TRANSACTION: 100_000
}

// 不同交易类型的推荐优先费用设置 (microLamports per compute unit)
export const PRIORITY_FEES = {
  // 创建交易：相对重要，设置适中优先费
  CREATE_TRANSACTION: 1000,
  // 投票交易：不太紧急，设置较低优先费
  VOTE_TRANSACTION: 500,
  // 执行交易：比较重要，设置适中优先费
  EXECUTE_TRANSACTION: 1000,
  // 取消交易：不太紧急，设置较低优先费
  CANCEL_TRANSACTION: 500
}
