import * as multisig from '@sqds/multisig'
import { Connection, PublicKey } from '@solana/web3.js'
import TOKENS from '../tokens.json' assert {type:'json'}
import { SPL_TOKEN_PUBKEY, SOLANA_RPC_URL, TX_MAX_RETRIES, SQUADS_PROGRAM_ID_V4_PUBKEY } from './constants.js'

const TOKEN_NAME_MAP = {}
for (const [symbol, { asset_id }] of Object.entries(TOKENS)) {
  TOKEN_NAME_MAP[asset_id] = symbol
}
export const getTokenName = mint => TOKEN_NAME_MAP[mint]
export const connection = new Connection(SOLANA_RPC_URL, 'confirmed')
export const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key])
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null
}
export const handleError = (ctx, error) => {
  console.error(`error: ${error}`)
  ctx.status = 200
  ctx.body = { error }
}

export async function getTokenAccounts(vaultPda) {
  const tokenAccounts = []
  try {
    const tokenAccountsResponse = await connection.getParsedTokenAccountsByOwner(vaultPda, { programId: SPL_TOKEN_PUBKEY})
    for (const { pubkey, account: { data: { parsed: { info: { mint, tokenAmount: { uiAmount, amount, decimals, uiAmountString }}}}}} of tokenAccountsResponse.value) {
      // 只包含有余额的 Token 账户
      if (uiAmount > 0) {
        tokenAccounts.push({
          address: pubkey.toBase58(),
          mint: mint,
          name: getTokenName(mint),
          balance: parseInt(amount),
          decimals: decimals,
          uiAmount: uiAmount,
          uiAmountString: uiAmountString
        })
      }
    }
  } catch (error) {
    console.error(`get token account error:`, error.message)
  }
  return tokenAccounts
}

export async function getCreatedatAndMemo(transactionPda) {
  let memo

  const signatures = await connection.getSignaturesForAddress(transactionPda, { limit: 5 })
  const { blockTime, signature } = signatures[signatures.length - 1]
  const { transaction: { message: { compiledInstructions, staticAccountKeys } } } = await connection.getTransaction(signature, { commitment: 'confirmed', maxSupportedTransactionVersion: 0 })

  for (const { programIdIndex, data } of compiledInstructions) {
    // 检查是否是 Squads 程序 & vault_transaction_create 的指令
    if (staticAccountKeys[programIdIndex].equals(SQUADS_PROGRAM_ID_V4_PUBKEY) && Buffer.from(data).slice(0, 8).equals(Buffer.from([48, 250, 78, 168, 208, 226, 218, 211]))) {
      // 解析 args
      const [{ memo: multisigMemo }] = multisig.generated.vaultTransactionCreateArgsBeet.deserialize(Buffer.from(data).slice(8))
      memo = multisigMemo
      break
    }
  }

  return {
    createdAt: new Date(blockTime * 1000).toISOString(),
    memo
  }
}

export const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(Buffer.from(signedTransaction, 'base64'), {
    skipPreflight: true,
    preflightCommitment: 'confirmed',
    maxRetries: TX_MAX_RETRIES,
    ...options
  })
  return signature
}

export async function parseTransfer(programId, instruction, message) {
  if (programId && programId.equals(SPL_TOKEN_PUBKEY)) {
    // SPL Token Program 转账
    const accountIndexes = Object.values(instruction.accountIndexes)
    const source = message.accountKeys[accountIndexes[0]].toBase58()
    const mint = message.accountKeys[accountIndexes[1]].toBase58()
    const destination = message.accountKeys[accountIndexes[2]].toBase58()

    const dataBuffer = Buffer.from(Object.values(instruction.data))
    const amount = dataBuffer.readBigUInt64LE(1)
    const decimals = dataBuffer.readUInt8(9)

    const { value: { data: { parsed: { info: { owner: from } } } } } = await connection.getParsedAccountInfo(new PublicKey(source))
    const { value: { data: { parsed: { info: { owner: to } } } } } = await connection.getParsedAccountInfo(new PublicKey(destination))

    return {
      chain: getTokenName(mint),
      amount: Number(amount) / Math.pow(10, decimals),
      from,
      to
    }
  } else {
    // System Program 转账 (SOL)
    const accountIndexes = Object.values(instruction.accountIndexes)
    const dataBuffer = Buffer.from(Object.values(instruction.data))
    let rawAmount
    try {
      rawAmount = dataBuffer.readBigUInt64LE(4)
    } catch (error) {
      rawAmount = 0
    }
    return {
      chain: 'sol',
      amount: Number(rawAmount) / Math.pow(10, 9),
      from: message.accountKeys[accountIndexes[0]].toBase58(),
      to: message.accountKeys[accountIndexes[1]].toBase58(),
    }
  }
}

// Squads 权限位掩码常量
const PERMISSION_MASKS = {
  PROPOSER: 0x01,  // 0b001 - 可以创建提案
  VOTER: 0x02,     // 0b010 - 可以投票
  EXECUTOR: 0x04,  // 0b100 - 可以执行交易
}

export function parsePermissions(mask) {
  const permissions = []
  if ((mask & PERMISSION_MASKS.PROPOSER) !== 0) permissions.push('Proposer')
  if ((mask & PERMISSION_MASKS.VOTER) !== 0) permissions.push('Voter')
  if ((mask & PERMISSION_MASKS.EXECUTOR) !== 0) permissions.push('Executor')
  return permissions
}
