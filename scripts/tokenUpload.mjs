import ApolloClient from '@common/apollo'
import { writeFileSync } from 'fs'
import { dirname, join } from 'path'
import { fileURLToPath } from 'url'
import { createMessage, readKey, encrypt, enums } from 'openpgp'
import { upload } from './utils.js'

const filename = fileURLToPath(import.meta.url)
const tokenPath = join(dirname(filename), '../tokens.bin')
const apolloUrl = process.argv[2]
const apolloAppid = process.argv[3]
const accessKey = process.argv[4]
const clusterName = process.argv[5] || null
const publicKeys = Buffer.from(process.argv[6], 'base64').toString('utf-8')
const baseAddress = '0000000000000000000000000000000'
const DATA_MAP = {}
const CHAIN_ID_MAP = {
  eth: 'chain_id_1',
  bnb1: 'chain_id_56',
  matic1: 'chain_id_137',
  opteth: 'chain_id_10',
  avax: 'chain_id_43114',
  arbieth: 'chain_id_42161',
  baseeth: 'chain_id_8453',
  sonic: 'chain_id_146'
}
const client = new ApolloClient({
  host: apolloUrl,
  appId: apolloAppid,
  accessKey,
  ...(clusterName && { clusterNames: [clusterName] }),
  namespaceList: ['chains']
})

async function initChain (key, chainData) {
  if (!chainData.assets) {
    return null
  }
  const datas = []
  const assetsKeys = Object.keys(chainData.assets)
  for (const asset of assetsKeys) {
    datas.push({
      decimals: chainData.assets[asset].precision || 18,
      symbol: asset,
      name: asset,
      contractAddress: `0x${chainData.assets[asset].contract_address || baseAddress}`
    })
  }
  DATA_MAP[key] = datas
}

async function encryptData (sourcedata) {
  const keys = []
  for (const key of JSON.parse(publicKeys)) {
    keys.push(await readKey({ armoredKey: key }))
  }
  const encryptData = await encrypt({
    message: await createMessage({ text: sourcedata }),
    encryptionKeys: keys,
    config: {
      preferredAEADAlgorithm: enums.symmetric.aes256
    }
  })
  return encryptData
}
async function generateAndUploadData () {
  // 初始化项目配置文件
  const configurations = await client.load()
  const chainKeys = Object.keys(configurations)
  for (const chain of chainKeys) {
    if (Object.keys(CHAIN_ID_MAP).indexOf(chain) > -1) {
      await initChain(CHAIN_ID_MAP[chain], configurations[chain])
    }
  }
  writeFileSync(tokenPath, await encryptData(JSON.stringify(DATA_MAP)))
  console.log('开始上传文件')
  await upload(tokenPath, 'tokens.bin')
  console.log('文件上传成功')
}

generateAndUploadData()
