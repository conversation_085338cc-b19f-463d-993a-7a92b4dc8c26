import { upload } from './awss3/upload.js'
import { dirname, join } from 'path'
import { fileURLToPath } from 'url'

const filename = fileURLToPath(import.meta.url)
const _dirname = dirname(filename)

const filePathx64 = join(_dirname, '../build', 'safe-bin-x64')
const filePatharm64 = join(_dirname, '../build', 'safe-bin-arm64')

upload(filePatharm64, 'safe-bin-arm64')
upload(filePathx64, 'safe-bin-x64')
