import { readFileSync, readdirSync, writeFileSync, mkdirSync } from 'fs'
import { fileURLToPath } from 'url'
import { dirname, join, extname } from 'path'

// 获取当前文件的目录路径（ES模块中的__dirname替代方案）
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const clientPath = join(__dirname, '../build/')

const slackwebhook = process.argv[2] || 'slackwebhook'
const s3Url = process.argv[3] || 'awss3url'

// 设置资源文件路径
const assetFile = join(__dirname, '../staticAssets.js')

// 获取所有前端资源
const assets = {}

// 递归读取前端目录中的所有文件
function readDir (dir, baseDir = '') {
  const files = readdirSync(dir, { withFileTypes: true })
  for (const file of files) {
    const filePath = join(dir, file.name)
    if (file.isDirectory()) {
      readDir(filePath, join(baseDir, file.name))
    } else {
      const relativePath = join(baseDir, file.name)
      const webPath = '/' + relativePath.replace(/\\/g, '/')

      // 确定内容类型
      let contentType = 'text/plain'
      const ext = extname(file.name).toLowerCase()

      if (ext === '.html') contentType = 'text/html'
      else if (ext === '.css') contentType = 'text/css'
      else if (ext === '.js') contentType = 'application/javascript'
      else if (ext === '.png') contentType = 'image/png'
      else if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg'
      else if (ext === '.gif') contentType = 'image/gif'
      else if (ext === '.svg') contentType = 'image/svg+xml'
      else if (ext === '.map') contentType = 'application/json'

      // 确定是否为二进制文件
      const isBinary = ['.png', '.jpg', '.jpeg', '.gif', '.ico', '.woff', '.woff2'].includes(ext)

      let content
      if (isBinary) {
        // 读取二进制文件并转为base64
        content = readFileSync(filePath).toString('base64')
        assets[webPath] = { content, contentType, isBase64: true }
      } else {
        // 读取文本文件
        content = readFileSync(filePath, 'utf-8')
        if (ext === '.map' || ext === '.js') {
          content = Buffer.from(content).toString('base64')
        }
        assets[webPath] = { content, contentType }
      }

      console.log(`处理资源: ${webPath} (${contentType})`)
    }
  }
}

// 读取所有前端资源
readDir(clientPath)

// 在服务器代码中替换静态资源对象,写入新的服务器文件
writeFileSync(assetFile, `const STATIC_ASSETS = ${JSON.stringify(assets, null, 2)} \nexport { STATIC_ASSETS }\n`)

// 替换server/util.js的敏感信息
const utilFile = join(__dirname, '../server/utils.js')
const utilCode = readFileSync(utilFile, 'utf-8')
writeFileSync(utilFile, utilCode.replace('slackwebhook', slackwebhook).replace('awss3url', s3Url))

// 替换install.sh 中的敏感信息
const installFile = join(__dirname, './install.sh')
const installCode = readFileSync(installFile, 'utf-8')
writeFileSync(installFile, installCode.replace('slackwebhook', slackwebhook).replace('awss3url', s3Url))
