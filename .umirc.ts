import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: 'Squads Wallet',
    locale: false,
  },
  routes: [
    {
      path: '/',
      redirect: '/send',
    },
    {
      name: 'Send',
      path: '/send',
      component: './Send',
      icon: 'SendOutlined',
    },
    {
      name: 'Transactions',
      path: '/transactions',
      component: './Transactions',
      icon: 'FileTextOutlined',
    },
    {
      name: 'Members',
      path: '/members',
      component: './Members',
      icon: 'TeamOutlined',
    }
  ],
  npmClient: 'pnpm',
  define: {
    'process.env': process.env,
  },
  // 禁用 findDOMNode 警告（仅开发环境）
  devtool: process.env.NODE_ENV === 'development' ? 'eval-cheap-module-source-map' : false,
  jsMinifierOptions: {
    target: ['chrome80', 'es2020']
  },
});

