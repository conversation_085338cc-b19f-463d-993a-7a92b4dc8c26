{"name": "squads-dapp", "version": "1.0.0", "description": "Squads wallet with client and server", "type": "module", "author": "", "license": "ISC", "homepage": ".", "scripts": {"start:server": "node server/index.js", "start:client:dev": "max dev", "start:client:build": "max build", "start:client:setup": "max setup", "start:client:start": "npm run dev", "start:client:postinstall": "max setup", "bundle": "node ./scripts/dappBundle.js", "build:client": "max build", "build:server": "bun build --compile --target=bun-darwin-arm64 server/index.js --outfile ./build/safe-bin-arm64 && bun build --compile --target=bun-darwin-x64 server/index.js --outfile ./build/safe-bin-x86_64", "build": "bun run build:client && bun run build:server", "upload:dapp": "node scripts/dappUpload.mjs", "upload:token": "node scripts/tokenUpload.mjs"}, "dependencies": {"koa": "2.15.4", "antd": "5.26.7", "buffer": "6.0.3", "axios": "1.11.0", "@koa/cors": "5.0.0", "node-fetch": "3.3.2", "koa-router": "12.0.1", "@umijs/max": "4.4.11", "koa-bodyparser": "4.4.1", "lucide-react": "0.534.0", "@sqds/multisig": "2.1.3", "@solana/web3.js": "1.98.2", "@ant-design/icons": "5.6.1", "@solana/spl-token": "0.4.13", "@ant-design/pro-components": "2.8.10", "@solana/wallet-adapter-base": "0.9.27", "@solana/wallet-adapter-react": "0.15.39", "@solana/wallet-adapter-wallets": "0.19.37", "@solana/wallet-adapter-react-ui": "0.9.39"}, "devDependencies": {"typescript": "^5.0.3", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11"}}